import { z } from "zod";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "@/components/custom-ui/toast";
import { useBusinessContext } from "@/context/BusinessContext";
import { useUserContext } from "@/context/UserContext";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import type { IRole, IRoleCreatePayload, IRoleUpdatePayload } from "@/types";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export const roleFormSchema = z.object({
	id: z.number().optional(),
	type: z.string().min(1, "Type is required"),
	name: z.string().min(1, "Name is required"),
	code: z.string().min(1, "Code is required").max(3, "Code must be 3 characters"),
	value: z.number().min(1, "Value must be a positive number"),
	overtimeRate: z.number().min(1, "Overtime rate must be a positive number"),
	smallTools: z.boolean(),
})

interface RoleFormProps {
	initialData?: IRole;
	mode?: "create" | "edit";
	onCancel: () => void;
}

export function RoleForm({ initialData, onCancel, mode = "create" }: RoleFormProps) {
	const { user } = useUserContext();
	const {
		entityActions: {
			createEntity,
			updateEntity
		},
		fetchActions: {
			fetchRoles
		}
	} = useBusinessContext()
	const { addToast } = useToast();

	const form = useForm<z.infer<typeof roleFormSchema>>({
		resolver: zodResolver(roleFormSchema),
		defaultValues: {
			id: initialData?.id || 0,
			type: initialData?.type || "",
			name: initialData?.name || "",
			code: initialData?.code || "",
			value: initialData?.value || 0,
			overtimeRate: initialData?.overtimeRate || 0,
			smallTools: initialData?.smallTools || false
		}
	})

	const handleSubmit = async (data: z.infer<typeof roleFormSchema>) => {
		if (!user) return addToast({
			type: "error",
			title: "Role creation failed",
			description: "You must be logged in to create a role."
		});

		if (mode === "create") {
			handleCreate(data);
		} else {
			handleEdit(data);
		}
	}

	const handleCreate = async (data: z.infer<typeof roleFormSchema>) => {
		const payload: IRoleCreatePayload = {
			type: data.type,
			name: data.name,
			code: data.code,
			value: data.value,
			overtimeRate: data.overtimeRate,
			smallTools: data.smallTools,
		}

		console.log(payload);

		const response = await createEntity<IRoleCreatePayload, IRole>("/v1/roles", payload)
		if (response.error) return addToast({
			type: "error",
			title: "Role creation failed",
			description: response.error || "Failed to create role"
		})
		addToast({
			type: "success",
			title: "Role created",
			description: "The role has been created successfully."
		})
		fetchRoles()
		onCancel();
	}

	const handleEdit = async (data: z.infer<typeof roleFormSchema>) => {
		if (!initialData || !data.id) return addToast({
			type: "error",
			title: "Role update failed",
			description: "Failed to update role. Role ID is missing."
		});

		const payload: IRoleUpdatePayload = {
			id: data.id,
			type: data.type,
			name: data.name,
			code: data.code,
			value: data.value,
			overtimeRate: data.overtimeRate,
			smallTools: data.smallTools,
		}
		const response = await updateEntity<IRoleUpdatePayload, IRole>(`/v1/roles/${initialData.id}`, payload)
		if (response.error) return addToast({
			type: "error",
			title: "Role update failed",
			description: response.error || "Failed to update role"
		})
		addToast({
			type: "success",
			title: "Role updated",
			description: "The role has been updated successfully."
		})
		fetchRoles()
		onCancel();
	}

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
				<div className="grid grid-cols-2 gap-4">

					<FormField
						control={form.control}
						name="type"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Type</FormLabel>
								<FormControl>
									<Select
										value={field.value}
										onValueChange={field.onChange}
									>
										<SelectTrigger>
											<SelectValue placeholder="Select type" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="management">Management</SelectItem>
											<SelectItem value="production">Production</SelectItem>
											<SelectItem value="support-staff">Support Staff</SelectItem>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="name"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Name</FormLabel>
								<FormControl>
									<Input placeholder="Enter role name" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="code"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Code</FormLabel>
								<FormControl>
									<Input placeholder="Enter role code" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="value"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Value</FormLabel>
								<FormControl>
									<Input
										type="number"
										placeholder="Enter role value"
										{...field}
										onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="overtimeRate"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Overtime Rate</FormLabel>
								<FormControl>
									<Input
										type="number"
										placeholder="Enter overtime rate"
										{...field}
										onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="smallTools"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Small Tools</FormLabel>
								<FormControl>
									<Select
										value={field.value ? "true" : "false"}
										onValueChange={(val) => field.onChange(val === "true")}
									>
										<SelectTrigger>
											<SelectValue placeholder="Select small tools" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="true">Yes</SelectItem>
											<SelectItem value="false">No</SelectItem>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

				</div>
				<div className="flex justify-end gap-2 pt-4">
					<Button
						type="button"
						variant="outline"
						onClick={onCancel}
					>
						Cancel
					</Button>
					<Button
						type="submit"
					>
						Save
					</Button>
				</div>
			</form>
		</Form>
	)
}
