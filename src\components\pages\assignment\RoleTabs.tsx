import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LoadingSpinner } from "@/components/custom-ui/loading";
import { useToast } from "@/components/custom-ui/toast";
import { useBusinessContext } from "@/context/BusinessContext";
import type {
  IRomRole,
  IRomRoleCreatePayload,
  IRomRoleUpdatePayload,
} from "@/types";
import { Save, Users, Plus, Trash2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface RoleTabsProps {
  romId: number;
  onUpdate?: () => void;
}

export function RoleTabs({ romId, onUpdate }: RoleTabsProps) {
  const [roleAssignments, setRoleAssignments] = useState<IRomRole[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [newRoleId, setNewRoleId] = useState<string>("");
  const [newQuantity, setNewQuantity] = useState<number>(1);

  const {
    entities: { roles },
    fetchActions: { fetchRoles, fetchEntity },
    entityActions: { createEntity, updateEntity, deleteEntity },
  } = useBusinessContext();
  const { addToast } = useToast();

  useEffect(() => {
    fetchRoles();
    fetchRoleAssignments();
  }, [romId]);

  const fetchRoleAssignments = async () => {
    setLoading(true);
    try {
      const response = await fetchEntity<{ results: IRomRole[] }>(
        `/v1/rom-roles/roms/${romId}`
      );
      if (response.data) {
        console.log("Role Assignments:", response.data.results);
        setRoleAssignments(response.data.results);
      } else if (response.error) {
        addToast({
          type: "error",
          title: "Failed to load role assignments",
          description: response.error,
        });
      }
    } catch (error) {
      addToast({
        type: "error",
        title: "Failed to load role assignments",
        description: `An unexpected error occurred: ${error}`,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleQuantityChange = (roleId: number, quantity: number) => {
    setRoleAssignments((prev) =>
      prev.map((assignment) =>
        assignment.roleId === roleId ? { ...assignment, quantity } : assignment
      )
    );
  };

  const handleSaveRole = async (roleId: number, quantity: number) => {
    setSaving(true);
    try {
      const payload: IRomRoleUpdatePayload = {
        romId,
        roleId,
        quantity,
      };

      const response = await updateEntity<IRomRoleUpdatePayload, IRomRole>(
        `/v1/rom-roles/roms/${romId}/roles/${roleId}`,
        payload
      );

      if (response.error) {
        addToast({
          type: "error",
          title: "Failed to update role",
          description: response.error,
        });
      } else {
        addToast({
          type: "success",
          title: "Role updated",
          description: "Role quantity has been updated successfully.",
        });
        onUpdate?.();
      }
    } catch (error) {
      addToast({
        type: "error",
        title: "Failed to update role",
        description: `An unexpected error occurred: ${error}`,
      });
    } finally {
      setSaving(false);
    }
  };

  const handleAddRole = async () => {
    if (!newRoleId || newQuantity <= 0) {
      addToast({
        type: "error",
        title: "Invalid input",
        description: "Please select a role and enter a valid quantity.",
      });
      return;
    }

    // Check if role is already assigned
    if (
      roleAssignments.some(
        (assignment) => assignment.roleId === parseInt(newRoleId)
      )
    ) {
      addToast({
        type: "error",
        title: "Role already assigned",
        description: "This role is already assigned to this ROM.",
      });
      return;
    }

    setSaving(true);
    try {
      const payload: IRomRoleCreatePayload = {
        romId,
        roleId: parseInt(newRoleId),
        quantity: newQuantity,
      };

      const response = await createEntity<IRomRoleCreatePayload, IRomRole>(
        `/v1/rom-roles`,
        payload
      );

      if (response.error) {
        addToast({
          type: "error",
          title: "Failed to add role",
          description: response.error,
        });
      } else {
        addToast({
          type: "success",
          title: "Role added",
          description: "Role has been added successfully.",
        });
        setNewRoleId("");
        setNewQuantity(1);
        fetchRoleAssignments();
        onUpdate?.();
      }
    } catch (error) {
      addToast({
        type: "error",
        title: "Failed to add role",
        description: `An unexpected error occurred: ${error}`,
      });
    } finally {
      setSaving(false);
    }
  };

  const handleRemoveRole = async (roleId: number) => {
    setSaving(true);
    try {
      const response = await deleteEntity(
        `/v1/rom-roles/roms/${romId}/roles/${roleId}`
      );

      if (response.error) {
        addToast({
          type: "error",
          title: "Failed to remove role",
          description: response.error,
        });
      } else {
        addToast({
          type: "success",
          title: "Role removed",
          description: "Role has been removed successfully.",
        });
        fetchRoleAssignments();
        onUpdate?.();
      }
    } catch (error) {
      addToast({
        type: "error",
        title: "Failed to remove role",
        description: `An unexpected error occurred: ${error}`,
      });
    } finally {
      setSaving(false);
    }
  };

  const availableRoles = roles.filter(
    (role) =>
      !roleAssignments.some((assignment) => assignment.roleId === role.id)
  );

  if (loading) {
    return (
      <Card className="shadow-sm">
        <CardContent className="flex items-center justify-center py-8">
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-6">
        <CardTitle className="flex items-center gap-3 text-xl font-semibold">
          <div className="p-2.5 bg-primary/10 dark:bg-primary/20 rounded-lg">
            <Users className="h-5 w-5 text-primary" />
          </div>
          ROM Roles Price List
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Existing Role Assignments */}
        {roleAssignments.length > 0 && (
          <div className="space-y-5">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-base text-foreground">
                Assigned Roles
              </h3>
              <div className="h-px bg-border flex-1" />
              <span className="text-sm text-muted-foreground bg-muted px-2 py-1 rounded-md">
                {roleAssignments.length} role{roleAssignments.length !== 1 ? 's' : ''}
              </span>
            </div>
            <div className="space-y-3">
              {roleAssignments.map((assignment) => (
                <div
                  key={assignment.roleId}
                  className="group relative p-5 border rounded-lg bg-card hover:bg-accent/5 transition-colors"
                >
                  <div className="flex gap-6">
                    {/* Role Information */}
                    <div className="flex flex-col items-start align-center flex-1 ">
                      <div className="flex gap-2 mb-2">
                        <h4 className="font-medium text-base text-foreground truncate">
                          {assignment.role.name}
                        </h4>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Hourly rate: <span className="font-medium text-foreground">${assignment.role.value.toLocaleString()}</span>
                      </div>
                    </div>

                    {/* Quantity and Total */}
                    <div className="flex items-center gap-4 flex-shrink-0">
                      <div className="flex items-center gap-2">
                        <Label
                          htmlFor={`quantity-${assignment.roleId}`}
                          className="text-sm font-medium whitespace-nowrap"
                        >
                          Hours:
                        </Label>
                        <Input
                          id={`quantity-${assignment.roleId}`}
                          type="number"
                          min="1"
                          value={assignment.quantity}
                          onChange={(e) =>
                            handleQuantityChange(
                              assignment.roleId,
                              parseInt(e.target.value) || 1
                            )
                          }
                          className="w-20 text-center"
                        />
                      </div>

                      <div className="flex items-center gap-2">
                        <Label className="text-sm font-medium whitespace-nowrap">
                          Total:
                        </Label>
                        <div className="min-w-0 px-3 py-1.5 bg-muted rounded-md">
                          <span className="text-sm font-semibold text-foreground">
                            ${(assignment.quantity * assignment.role.value).toLocaleString()}
                          </span>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-1.5 ml-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            handleSaveRole(assignment.roleId, assignment.quantity)
                          }
                          disabled={saving}
                          className="h-8 px-3"
                        >
                          <Save className="h-3.5 w-3.5" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleRemoveRole(assignment.roleId)}
                          disabled={saving}
                          className="h-8 px-3"
                        >
                          <Trash2 className="h-3.5 w-3.5" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Add New Role */}
        {availableRoles.length > 0 && (
          <div className="space-y-5">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-base text-foreground">
                Add New Role
              </h3>
              <div className="h-px bg-border flex-1" />
              <span className="text-sm text-muted-foreground bg-muted px-2 py-1 rounded-md">
                {availableRoles.length} available
              </span>
            </div>
            <div className="p-6 border-2 border-dashed border-muted-foreground/20 rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors">
              <div className="flex items-end gap-4">
                <div className="flex-1 space-y-2">
                  <Label htmlFor="new-role" className="text-sm font-medium">
                    Select Role
                  </Label>
                  <Select value={newRoleId} onValueChange={setNewRoleId}>
                    <SelectTrigger className="bg-background">
                      <SelectValue placeholder="Choose a role to assign" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableRoles.map((role) => (
                        <SelectItem key={role.id} value={role.id.toString()}>
                          <div className="flex items-center justify-between w-full">
                            <span className="font-medium">{role.name}</span>
                            <span className="text-muted-foreground ml-2">
                              ${role.value.toLocaleString()}/hr
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-quantity" className="text-sm font-medium">
                    Hours
                  </Label>
                  <Input
                    id="new-quantity"
                    type="number"
                    min="1"
                    value={newQuantity}
                    onChange={(e) =>
                      setNewQuantity(parseInt(e.target.value) || 1)
                    }
                    className="w-24 text-center bg-background"
                    placeholder="1"
                  />
                </div>
                <Button
                  onClick={handleAddRole}
                  disabled={saving || !newRoleId}
                  className="h-9 px-4"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Role
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* No available roles message */}
        {availableRoles.length === 0 && roleAssignments.length > 0 && (
          <div className="p-4 border border-muted rounded-lg bg-muted/20">
            <p className="text-sm text-muted-foreground text-center">
              All available roles have been assigned to this ROM.
            </p>
          </div>
        )}

        {roleAssignments.length === 0 && (
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              No roles assigned yet
            </h3>
            <p className="text-muted-foreground max-w-sm mx-auto">
              Get started by adding roles to this ROM using the form {availableRoles.length > 0 ? 'above' : 'once roles are available'}.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
