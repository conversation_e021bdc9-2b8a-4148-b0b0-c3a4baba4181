import { useEffect, useState, useCallback, useRef } from "react";
import $ from 'jquery';
import {
  RefreshCw,
  Upload,
  Download,
  FileText,
  File,
  Eye,
  Calendar,
  User,
  HardDrive,
  CheckCircle,
  Clock,
  AlertCircle,
  Plus,
  Trash2
} from "lucide-react";
import { useBusinessContext } from "@/context/BusinessContext";
import { useToast } from "@/components/custom-ui/toast";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/custom-ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { LoadingSpinner, LoadingState, EmptyState, ErrorState } from "@/components/custom-ui/loading";
import type { IDocumentIntentCreatePayload, IDocumentIntentCreateResponse } from "@/types";
import { downloadFromApi, postFileToApi, postToApi } from "@/lib/api";
import type { IDocument } from "@/types/domain";

interface UploadState {
  file: File | null;
  token: string | null;
  type: string;
  uploading: boolean;
  progress: number;
}

const N8N_URL = import.meta.env.VITE_N8N_URL;

export function DocumentsView({ assignmentId }: { assignmentId: number }) {
  const [documents, setDocuments] = useState<IDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [uploadState, setUploadState] = useState<UploadState>({
    file: null,
    token: null,
    type: "invoice",
    uploading: false,
    progress: 0
  });
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    fetchActions: { fetchEntity },
    entityActions: { deleteEntity }
  } = useBusinessContext();
  const { addToast } = useToast();

  const formatFileSize = useCallback((bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  const getStatusVariant = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'uploaded': return 'default';
      case 'processing': return 'secondary';
      case 'failed': return 'destructive';
      default: return 'outline';
    }
  }, []);

  const getStatusIcon = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'uploaded': return CheckCircle;
      case 'processing': return Clock;
      case 'failed': return AlertCircle;
      default: return Clock;
    }
  }, []);

  const fetchDocuments = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetchEntity<IDocument[]>(`/v1/documents/${assignmentId}`);
      if (response.data) {
        setDocuments(response.data);
        console.log('Documents fetched successfully:', response.data);
      } else if (response.error) {
        setError(response.error);
        addToast({
          type: 'error',
          title: 'Error fetching documents',
          description: response.error
        });
      }
    } catch (error) {
      setError(error as string);
      addToast({
        type: 'error',
        title: 'Error fetching documents',
        description: 'An error occurred while fetching documents. Please try again later.'
      });
    } finally {
      setLoading(false);
    }
  }, [assignmentId, fetchEntity, addToast]);

  const postTon8n = useCallback(async (assignmentId: number, documentId: number) => {
    const req = {
      assignmentId,
      documentId
    }

    console.log(JSON.stringify(req))
    try {
      $.ajax({
        url: N8N_URL,
        method: "POST",
        Content-Type: 
        mode: "no-cors",
        body: JSON.stringify(req)
      });

      console.log("Request sent (no-cors), response cannot be read");
    
      addToast({
        type: "success",
        title: "Request sent",
        description: "The request was sent, but response cannot be read due to no-cors"
      });

    } catch (error) {
      console.error("Error posting to n8n:", error);
      addToast({
        type: "error",
        title: "Failed to post to n8n",
        description: error instanceof Error ? error.message : "An error occurred while posting to n8n"
      })
    }
  }, [addToast]);

  const handleGenerateReport = useCallback(async (assignmentId: number) => {
    const lastDocument = documents[documents.length - 1];
    if (!lastDocument) {
      addToast({
        type: "error",
        title: "No document found",
        description: "No document found to generate report from."
      });
      return;
    }
    await postTon8n(assignmentId, lastDocument.id);

    addToast({
      type: "success",
      title: "Generating report",
      description: "The report has been sent to the agent."
    });
  }, [documents]);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];

    // Validate file size (30MB limit)
    if (file.size > 30 * 1024 * 1024) {
      addToast({
        type: "error",
        title: "File too large",
        description: "Please select a file smaller than 30MB."
      });
      return;
    }

    setUploadState(prev => ({
      ...prev,
      file,
      progress: 0
    }));
  }, [addToast]);

  const resetUpload = useCallback(() => {
    setUploadState({
      file: null,
      token: null,
      type: "invoice",
      uploading: false,
      progress: 0
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const handleUpload = useCallback(async () => {
    if (!uploadState.file) {
      addToast({
        type: "error",
        title: "No file selected",
        description: "Please select a file to upload."
      });
      return;
    }

    setUploadState(prev => ({ ...prev, uploading: true, progress: 10 }));

    try {
      const req: IDocumentIntentCreatePayload = {
        assignmentId,
        fileName: uploadState.file.name,
        mimeType: uploadState.file.type,
        size: uploadState.file.size,
        type: uploadState.type
      };

      setUploadState(prev => ({ ...prev, progress: 30 }));

      const intentResponse = await postToApi<IDocumentIntentCreateResponse, IDocumentIntentCreatePayload>(`/v1/documents`, req, true);

      if (intentResponse.error || !intentResponse.data) {
        throw new Error(intentResponse.error?.message || "Failed to create document intent");
      }

      setUploadState(prev => ({ ...prev, progress: 50, token: intentResponse.data!.uploadToken }));

      const uploadResponse = await postFileToApi<IDocument>(`/v1/documents/upload?token=${intentResponse.data.uploadToken}`, uploadState.file, undefined, true);

      if (uploadResponse.error || !uploadResponse.data) {
        throw new Error(uploadResponse.error?.message || "Failed to upload document");
      }

      setUploadState(prev => ({ ...prev, progress: 100 }));

      addToast({
        type: "success",
        title: "Document uploaded",
        description: "The document has been uploaded successfully."
      });

      postTon8n(assignmentId, uploadResponse.data.id);

      await fetchDocuments();
      resetUpload();
      setIsUploadDialogOpen(false);
    } catch (error) {
      addToast({
        type: "error",
        title: "Upload failed",
        description: error instanceof Error ? error.message : "An error occurred during upload"
      });
      setUploadState(prev => ({ ...prev, uploading: false, progress: 0 }));
    }
  }, [uploadState, assignmentId, addToast, fetchDocuments, resetUpload]);

  const handleDownloadAll = useCallback(async () => {
    try {
      const response = await downloadFromApi(`/v1/documents/bundle/${assignmentId}`);

      if (response.error) {
        addToast({
          type: "error",
          title: "Error downloading documents",
          description: response.error.message,
        });
        return;
      }

      if (response.data) {
        const url = window.URL.createObjectURL(response.data);
        const disposition = response.data.type;
        let filename = `assignment_${assignmentId}`;

        if (disposition.includes("pdf")) {
          filename += ".pdf";
        } else if (disposition.includes("zip")) {
          filename += ".zip";
        } else {
          filename += ".bin";
        }

        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);

        addToast({
          type: "success",
          title: "Download started",
          description: "Your documents bundle is being downloaded."
        });
      }
    } catch (_error) {
      addToast({
        type: "error",
        title: "Download failed",
        description: "An error occurred while downloading documents."
      });
    }
  }, [assignmentId]);

  const handleDownload = useCallback(async (doc: IDocument) => {
    try {
      const response = await downloadFromApi(`/v1/documents/file/${doc.id}`);

      if (response.error) {
        addToast({
          type: "error",
          title: "Download failed",
          description: response.error.message,
        });
        return;
      }

      if (response.data) {
        const url = window.URL.createObjectURL(response.data);
        const a = document.createElement("a");
        a.href = url;
        a.download = doc.fileName;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);

        addToast({
          type: "success",
          title: "Download started",
          description: `${doc.fileName} is being downloaded.`
        });
      }
    } catch (_error) {
      addToast({
        type: "error",
        title: "Download failed",
        description: "An error occurred while downloading the document."
      });
    }
  }, []);

  const handleDelete = useCallback(async (doc: IDocument) => {
    const response = await deleteEntity(`/v1/documents/${doc.id}`);
    if (response.error) {
      addToast({
        type: "error",
        title: "Delete failed",
        description: response.error || "Failed to delete document"
      });
      return;
    }

    addToast({
      type: "success",
      title: "Document deleted",
      description: "The document has been deleted successfully."
    });
    fetchDocuments();
  }, []);

  useEffect(() => {
    if (assignmentId) {
      fetchDocuments();
    }
  }, [assignmentId, fetchDocuments]);

  const uploadedDocuments = documents.filter(doc => doc.status === "uploaded");
  const processingDocuments = documents.filter(doc => doc.status === "processing");
  const failedDocuments = documents.filter(doc => doc.status === "failed");

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-muted-foreground">Manage assignment documents</p>
          </div>
        </div>
        <LoadingState message="Loading documents..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Documents</h2>
            <p className="text-muted-foreground">Manage assignment documents</p>
          </div>
        </div>
        <ErrorState
          title="Failed to load documents"
          description={error}
          action={
            <Button onClick={fetchDocuments} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Dialog open={isReportDialogOpen} onOpenChange={setIsReportDialogOpen}>
        <DialogContent className="w-[50vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle>Report</DialogTitle>
          </DialogHeader>
          <p>Reports are generated once the document is uploaded. Are you sure you want to generate it again manually? (The last uploaded document will be used.)</p>
          <div className="flex justify-end gap-2 mt-6">
            <Button
              variant="outline"
              onClick={() => setIsReportDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                handleGenerateReport(assignmentId)
                setIsReportDialogOpen(false);
              }}
            >
              Generate
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <p className="text-muted-foreground">
            Manage assignment documents ({documents.length} total)
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={fetchDocuments} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          {uploadedDocuments.length > 0 && (
            <Button onClick={handleDownloadAll} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download All
            </Button>
          )}
          <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Upload Document</DialogTitle>
                <DialogDescription>
                  Select a document to upload to this assignment.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="document-type">Document Type</Label>
                  <Select
                    value={uploadState.type}
                    onValueChange={(value) => setUploadState(prev => ({ ...prev, type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select document type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="invoice">Invoice</SelectItem>
                      <SelectItem value="estimate" disabled>Estimate</SelectItem>
                      <SelectItem value="receipt" disabled>Receipt</SelectItem>
                      <SelectItem value="report" disabled>Report</SelectItem>
                      <SelectItem value="photo" disabled>Photo</SelectItem>
                      <SelectItem value="other" disabled>Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="file-input">File</Label>
                  <input
                    ref={fileInputRef}
                    id="file-input"
                    type="file"
                    onChange={handleFileSelect}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2
                               file:px-4 file:rounded-md file:border-0
                               file:text-sm file:font-medium
                               file:bg-primary/10 file:text-primary
                               hover:file:bg-primary/20 cursor-pointer"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.zip"
                    disabled={uploadState.uploading}
                  />
                  <p className="text-xs text-muted-foreground">
                    Supported formats: PDF, DOC, DOCX, JPG, PNG, GIF, ZIP (max 30MB)
                  </p>
                </div>

                {uploadState.file && (
                  <div className="p-3 bg-muted rounded-md">
                    <div className="flex items-center gap-2">
                      <File className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{uploadState.file.name}</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatFileSize(uploadState.file.size)}
                    </p>
                  </div>
                )}

                {uploadState.uploading && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Uploading...</span>
                      <span>{uploadState.progress}%</span>
                    </div>
                    <Progress value={uploadState.progress} className="h-2" />
                  </div>
                )}

                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      resetUpload();
                      setIsUploadDialogOpen(false);
                    }}
                    disabled={uploadState.uploading}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleUpload}
                    disabled={!uploadState.file || uploadState.uploading}
                  >
                    {uploadState.uploading ? (
                      <>
                        <LoadingSpinner size="sm" className="mr-2" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Upload
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Main Content */}
      {documents.length === 0 ? (
        <EmptyState
          title="No documents found"
          description="Upload your first document to get started."
          icon={<FileText className="h-12 w-12" />}
          action={
            <Button onClick={() => setIsUploadDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Upload Document
            </Button>
          }
        />
      ) : (
        <div>
          <Tabs defaultValue="all" className="space-y-4">
            <div className="w-full flex justify-between">
              <TabsList>
                <TabsTrigger value="all">All ({documents.length})</TabsTrigger>
                <TabsTrigger value="uploaded">
                  Uploaded ({uploadedDocuments.length})
                </TabsTrigger>
              </TabsList>

              <Button
                onClick={() => {
                  setIsReportDialogOpen(true)
                }}
                disabled={uploadedDocuments.length === 0}
                >
                Generate Report
            </Button>
            </div>

            <TabsContent value="all" className="space-y-4">
              <DocumentList documents={documents} onDownload={handleDownload} />
            </TabsContent>

            <TabsContent value="uploaded" className="space-y-4">
              <DocumentList
                documents={uploadedDocuments}
                onDownload={handleDownload}
              />
            </TabsContent>

          {processingDocuments.length > 0 && (
            <TabsContent value="processing" className="space-y-4">
              <DocumentList documents={processingDocuments} onDownload={handleDownload} />
            </TabsContent>
          )}

            {failedDocuments.length > 0 && (
              <TabsContent value="failed" className="space-y-4">
                <DocumentList
                  documents={failedDocuments}
                  onDownload={handleDownload}
                />
              </TabsContent>
            )}
          </Tabs>
        </div>
      )}
    </div>
  );

  function DocumentList({ documents, onDownload }: { documents: IDocument[], onDownload: (doc: IDocument) => void }) {
    if (documents.length === 0) {
      return (
        <div className="text-center py-8">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No documents in this category</p>
        </div>
      );
    }

    return (
      <div className="grid gap-4">
        {documents.map((doc) => {
          const StatusIcon = getStatusIcon(doc.status);

          return (
            <Card key={doc.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                        <File className="h-5 w-5 text-primary" />
                      </div>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium truncate">{doc.fileName}</h3>
                        <Badge variant={getStatusVariant(doc.status)} className="flex items-center gap-1">
                          <StatusIcon className="h-3 w-3" />
                          {doc.status}
                        </Badge>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <HardDrive className="h-3 w-3" />
                          {formatFileSize(doc.size)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {doc.createdAt}
                        </div>
                        {doc.createdBy && (
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            {doc.createdBy.name}
                          </div>
                        )}
                      </div>

                      <div className="mt-2">
                        <Badge variant="outline" className="text-xs">
                          {doc.type}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    {doc.status === "uploaded" && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onDownload(doc)}
                          className="flex items-center gap-1"
                        >
                          <Download className="h-3 w-3" />
                          Download
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            console.log("Preview", doc);
                          }}
                          disabled
                          className="flex items-center gap-1"
                        >
                          <Eye className="h-3 w-3" />
                          Preview
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => {
                            handleDelete(doc)
                          }}
                          className="flex items-center gap-1"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </>
                    )}

                    {doc.status === "failed" && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          console.log("Retry", doc);
                        }}
                        className="flex items-center gap-1"
                      >
                        <RefreshCw className="h-3 w-3" />
                        Retry
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  }
}