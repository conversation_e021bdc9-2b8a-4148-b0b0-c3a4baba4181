import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, Users, Plus } from 'lucide-react';
import { useBusinessContext } from '@/context/BusinessContext';
import type { IRole } from '@/types';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { RoleForm } from '@/components/pages/roles/RoleForm';
import { useToast } from '@/components/custom-ui/toast';

export function Roles() {
  const [isRoleFormDialogOpen, setIsRoleFormDialogOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<IRole | null>(null);
  const [deletingRole, setDeletingRole] = useState<IRole | null>(null);
  const {
    entities: { roles },
    loading,
    contextError,
    fetchActions: { fetchRoles },
    entityActions: { deleteEntity }
  } = useBusinessContext();

  const { addToast } = useToast();

  const handleDeleteRole = async (roleId: number) => {
    const response = await deleteEntity(`/v1/roles/${roleId}`);
    if (response.error) {
      addToast({
        type: "error",
        title: "Failed to delete role",
        description: response.error,
      });
    } else {
      addToast({
        type: "success",
        title: "Role deleted",
        description: "The role has been deleted successfully.",
      });
      fetchRoles();
    }
  };

  useEffect(() => {
    fetchRoles();
  }, []);

  const columns: Column<IRole>[] = [
    {
      key: 'type',
      header: 'Type',
      sortable: true,
      render: (value) => (
        <div className="font-medium">
          {value}
        </div>
      )
    },
    {
      key: 'name',
      header: 'Role Name',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-blue-100 text-blue-800 flex items-center justify-center">
            <Users className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium">{value}</div>
          </div>
        </div>
      )
    },
    {
      key: 'code',
      header: 'Code',
      sortable: true,
      render: (value) => (
        <div className="font-medium">
          {value}
        </div>
      )
    },
    {
      key: 'value',
      header: 'Value',
      sortable: true,
      render: (value) => (
        <div className="font-medium">
          ${value.toLocaleString()}
        </div>
      )
    },
    {
      key: "overtimeRate",
      header: "Overtime Rate",
      sortable: true,
      render: (value) => (
        <div className="font-medium">
          {value}
        </div>
      )
    },
    {
      key: 'smallTools',
      header: 'Small Tools',
      sortable: true,
      render: (value) => (
        <div className="font-medium">
          {value ? 'Yes' : 'No'}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setEditingRole(row);
              setIsRoleFormDialogOpen(true);
            }}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setDeletingRole(row);
            }}
            className='cursor-pointer'
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Roles"
        description="Manage worker roles and their associated values"
      >
        <Button onClick={() => {
          setEditingRole(null);
          setIsRoleFormDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Add Role
        </Button>
      </PageHeader>

      <PageContent>
        {contextError && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            <p>Error: {contextError}</p>
          </div>
        )}
        <DataTable
          data={roles}
          columns={columns}
          searchPlaceholder="Search roles..."
          onRowClick={(role) => console.log('View role:', role)}
          loading={loading.roles}
        />
      </PageContent>

      {/* Add/Edit Dialog */}
      <Dialog open={isRoleFormDialogOpen} onOpenChange={setIsRoleFormDialogOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {editingRole ? 'Edit Role' : 'Add New Role'}
            </DialogTitle>
          </DialogHeader>
          <RoleForm
            mode={editingRole ? 'edit' : 'create'}
            initialData={editingRole || undefined}
            onCancel={() => setIsRoleFormDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={!!deletingRole} onOpenChange={() => setDeletingRole(null)}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              Delete Role
            </DialogTitle>
          </DialogHeader>
          <div className="p-4 space-y-4">
            <p className="text-lg font-medium">Are you sure you want to delete this role?</p>
            <p className="text-sm text-muted-foreground">This action cannot be undone.</p>
          </div>
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setDeletingRole(null)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={() => {
                handleDeleteRole(deletingRole?.id || 0);
                setDeletingRole(null);
              }}
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
